import{d as ee,j as le,k as P,l as te,q as B,s as oe,i as ne,c as m,o as d,w as a,b as l,F as x,x as E,u as t,a as _,y as r,z as se,M as $,A as j,t as U,B as F,C as p,D as V,E as I,G as ae,H as ie,e as R,I as h,n as O,_ as ue,J as z,K as de}from"./index-DOp68rd-.js";import{_ as re}from"./IconTitle.vue_vue_type_script_setup_true_lang-BXngeGY8.js";const pe={"h-full":"","w-full":"","of-auto":"",px8:"",py6:""},me={grid:"~ md:cols-[repeat(auto-fit,minmax(16rem,1fr))] gap-x-10 gap-y-3","max-w-300":""},ve={flex:"~ col gap-2"},fe={flex:"~ gap-2","flex-auto":"","items-center":"","justify-start":""},xe={capitalize:"",op75:""},ge={flex:"~ gap-2","flex-auto":"","items-center":"","justify-start":"","pr-4":"","text-sm":""},ce=["onClick"],be=["onClick"],ye=["onClick"],Ve={flex:"~ col gap-2"},ke={flex:"~ gap2"},Ce={class:"flex items-center gap2 text-sm"},we={class:"flex items-center gap2 text-sm"},Se={class:"flex items-center gap2 text-sm"},Te={class:"flex items-center gap2 text-sm"},_e={flex:"~ gap-2"},ze=ee({__name:"settings",setup(Ue){const{categorizedTabs:H}=le(),D=ie(),q=D==="iframe"||D==="separate-window",{scale:k,interactionCloseOnOutsideClick:C,showPanel:w,minimizePanelInteractive:g,expandSidebar:S,scrollableSidebar:T,reduceMotion:G}=P(te(B)),J=[["Tiny",12/15],["Small",14/15],["Normal",1],["Large",16/15],["Huge",18/15]],K=[["Always",0],["1s",1e3],["2s",2e3],["5s",5e3],["10s",1e4],["Never",-1]],{hiddenTabCategories:c,hiddenTabs:b,pinnedTabs:i}=P(B.value.tabSettings);function Q(s,e){e?b.value=b.value.filter(u=>u!==s):b.value.push(s)}function W(s,e){e?c.value=c.value.filter(u=>u!==s):c.value.push(s)}function X(s){i.value.includes(s)?i.value=i.value.filter(e=>e!==s):i.value.push(s)}function A(s,e){const u=i.value.indexOf(s);if(u===-1)return;const v=u+e;if(v<0||v>=i.value.length)return;const o=[...i.value];o.splice(u,1),o.splice(v,0,s),i.value=o}const y=oe(!1);async function Y(){de(),window.location.reload()}const L=K.map(([s,e])=>({label:s,value:e})),Z=ne(()=>`${L.find(e=>e.value===g.value)?.label??"Select..."}`);return(s,e)=>{const u=re,v=ue;return d(),m("div",pe,[a(u,{class:"mb-5 text-xl op75",icon:"i-carbon-settings-adjust",text:"DevTools Settings"}),l("div",me,[l("div",ve,[e[12]||(e[12]=l("h3",{"text-lg":""}," Tabs ",-1)),(d(!0),m(x,null,E(t(H),([{name:o,hidden:f},M])=>(d(),m(x,{key:o},[M.length?(d(),R(t(I),{key:0,p3:"",flex:"~ col gap-1",class:O(f?"op50 grayscale":"")},{default:r(()=>[a(t(h),{"model-value":!t(c).includes(o),class:"row-reverse flex py1 pl2 pr1 hover:bg-active","onUpdate:modelValue":n=>W(o,n)},{default:r(()=>[l("div",fe,[l("span",xe,U(o),1)])]),_:2},1032,["model-value","onUpdate:modelValue"]),e[11]||(e[11]=l("div",{"mx--1":"",my1:"","h-1px":"",border:"b base",op75:""},null,-1)),(d(!0),m(x,null,E(M,n=>(d(),R(t(h),{key:n.name,class:O(["row-reverse n-primary flex py1 pl2 pr1 hover:bg-active",n.hidden?"op35":""]),"model-value":!t(b).includes(n.name),"onUpdate:modelValue":N=>Q(n.name,N)},{default:r(()=>[l("div",ge,[a(v,{"text-xl":"",icon:n.icon,fallback:n.fallbackIcon,title:n.title},null,8,["icon","fallback","title"]),l("span",null,U(n.title),1),e[10]||(e[10]=l("div",{"flex-auto":""},null,-1)),t(i).includes(n.name)?(d(),m(x,{key:0},[l("button",{class:"flex items-center px1 py1 text-sm op65 hover:bg-active hover:op100",onClick:z(()=>{t(i).indexOf(n.name)!==0&&A(n.name,-1)},["stop"])},e[8]||(e[8]=[l("div",{class:"i-carbon-caret-up"},null,-1)]),8,ce),l("button",{class:"flex items-center px1 py1 text-sm op65 hover:bg-active hover:op100",onClick:z(()=>{t(i).indexOf(n.name)!==t(i).length-1&&A(n.name,1)},["stop"])},e[9]||(e[9]=[l("div",{class:"i-carbon-caret-down"},null,-1)]),8,be)],64)):_("",!0),l("button",{class:"flex items-center px1 py1 text-sm op65 hover:bg-active hover:op100",onClick:z(N=>X(n.name),["stop"])},[l("div",{class:O(t(i).includes(n.name)?" i-carbon-pin-filled rotate--45":" i-carbon-pin op45")},null,2)],8,ye)])]),_:2},1032,["model-value","class","onUpdate:modelValue"]))),128))]),_:2,__:[11]},1032,["class"])):_("",!0)],64))),128))]),l("div",Ve,[e[25]||(e[25]=l("h3",{"text-lg":""}," Appearance ",-1)),a(t(I),{p4:"",flex:"~ col gap-2"},{default:r(()=>[l("div",ke,[a(t(se),{animation:!t(G)},{default:r(({isDark:o,toggle:f})=>[a(t($),{outlined:"",type:"primary",onClick:f},{default:r(()=>[e[13]||(e[13]=l("div",{"i-carbon-sun":"","dark:i-carbon-moon":"","translate-y--1px":""},null,-1)),j(" "+U(o?"Dark":"Light"),1)]),_:2,__:[13]},1032,["onClick"])]),_:1},8,["animation"])]),e[16]||(e[16]=l("div",{"mx--2":"",my1:"","h-1px":"",border:"b base",op75:""},null,-1)),e[17]||(e[17]=l("p",null,"UI Scale",-1)),l("div",null,[a(t(F),{modelValue:t(k),"onUpdate:modelValue":e[0]||(e[0]=o=>p(k)?k.value=o:null),options:J.map(([o,f])=>({label:o,value:f})),"button-props":{outlined:!0}},null,8,["modelValue","options"])]),e[18]||(e[18]=l("div",{"mx--2":"",my1:"","h-1px":"",border:"b base",op75:""},null,-1)),l("div",Ce,[a(t(V),{modelValue:t(S),"onUpdate:modelValue":e[1]||(e[1]=o=>p(S)?S.value=o:null)},null,8,["modelValue"]),e[14]||(e[14]=l("span",{op75:""},"Expand Sidebar",-1))]),l("div",we,[a(t(V),{modelValue:t(T),"onUpdate:modelValue":e[2]||(e[2]=o=>p(T)?T.value=o:null)},null,8,["modelValue"]),e[15]||(e[15]=l("span",{op75:""},"Scrollable Sidebar",-1))])]),_:1,__:[16,17,18]}),t(q)?(d(),m(x,{key:0},[e[23]||(e[23]=l("h3",{mt2:"","text-lg":""}," Features ",-1)),a(t(I),{p4:"",flex:"~ col gap-2"},{default:r(()=>[l("div",Se,[a(t(V),{modelValue:t(C),"onUpdate:modelValue":e[3]||(e[3]=o=>p(C)?C.value=o:null)},null,8,["modelValue"]),e[19]||(e[19]=l("span",{op75:""},"Close DevTools when clicking outside",-1))]),l("div",Te,[a(t(V),{modelValue:t(w),"onUpdate:modelValue":e[4]||(e[4]=o=>p(w)?w.value=o:null)},null,8,["modelValue"]),e[20]||(e[20]=l("span",{op75:""},"Always show the floating panel",-1))]),e[21]||(e[21]=l("div",{"mx--2":"",my1:"","h-1px":"",border:"b base",op75:""},null,-1)),e[22]||(e[22]=l("p",null,"Minimize floating panel on inactive",-1)),l("div",null,[a(t(F),{modelValue:t(g),"onUpdate:modelValue":e[5]||(e[5]=o=>p(g)?g.value=o:null),"button-props":{outlined:!0},options:t(L),placeholder:t(Z)},null,8,["modelValue","options","placeholder"])])]),_:1,__:[21,22]})],64)):_("",!0),e[26]||(e[26]=l("h3",{mt2:"","text-lg":""}," Debug ",-1)),l("div",_e,[a(t($),{outlined:"",type:"warning",onClick:e[6]||(e[6]=o=>y.value=!0)},{default:r(()=>e[24]||(e[24]=[l("div",{"i-carbon-breaking-change":""},null,-1),j(" Reset Local Settings & State ",-1)])),_:1,__:[24]}),a(t(ae),{modelValue:t(y),"onUpdate:modelValue":e[7]||(e[7]=o=>p(y)?y.value=o:null),title:"Clear Local Settings & State",width:"40%",height:"200px",content:"Are you sure you to reset all local settings & state? Devtools will reload.",onConfirm:Y},null,8,["modelValue"])])])])])}}});export{ze as default};
